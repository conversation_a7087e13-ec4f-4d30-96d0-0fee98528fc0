import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import prisma from '@/config/prismaClient';

// Get all bank payments
export const getAllBankPayments = async (req: Request, res: Response): Promise<any> => {
  try {
    const bankPayments = await prisma.bankPayment.findMany({
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return sendSuccess(res, bankPayments, 'Bank payments retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payments:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get bank payments by class ID
export const getBankPaymentsByClassId = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;

    const bankPayments = await prisma.bankPayment.findMany({
      where: { classId },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return sendSuccess(res, bankPayments, 'Bank payments retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payments:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Create new bank payment
export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      classId,
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    } = req.body;

    // Check if class exists
    const classExists = await prisma.classes.findUnique({
      where: { id: classId }
    });

    if (!classExists) {
      return sendError(res, 'Class not found', 404);
    }

    const bankPayment = await prisma.bankPayment.create({
      data: {
        classId,
        bankName: bankName.toUpperCase(),
        accountNumber,
        reAccountNumber,
        ifscCode: ifscCode.toUpperCase(),
        accountHolderName: accountHolderName.toUpperCase(),
        branchName: branchName.toUpperCase()
      },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    return sendSuccess(res, bankPayment, 'Bank payment created successfully');
  } catch (error) {
    console.error('Error creating bank payment:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Update bank payment
export const updateBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    } = req.body;

    const bankPayment = await prisma.bankPayment.update({
      where: { id },
      data: {
        bankName: bankName.toUpperCase(),
        accountNumber,
        reAccountNumber,
        ifscCode: ifscCode.toUpperCase(),
        accountHolderName: accountHolderName.toUpperCase(),
        branchName: branchName.toUpperCase()
      },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    return sendSuccess(res, bankPayment, 'Bank payment updated successfully');
  } catch (error) {
    console.error('Error updating bank payment:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Delete bank payment
export const deleteBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    await prisma.bankPayment.delete({
      where: { id }
    });

    return sendSuccess(res, null, 'Bank payment deleted successfully');
  } catch (error) {
    console.error('Error deleting bank payment:', error);
    return sendError(res, 'Internal server error', 500);
  }
};
