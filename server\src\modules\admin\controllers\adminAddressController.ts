import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import prisma from '@/config/prismaClient';

// Get all addresses
export const getAllAddresses = async (req: Request, res: Response): Promise<any> => {
  try {
    const addresses = await prisma.classesAddress.findMany({
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return sendSuccess(res, addresses, 'Addresses retrieved successfully');
  } catch (error) {
    console.error('Error fetching addresses:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get address by class ID
export const getAddressByClassId = async (req: Request, res: Response): Promise<any> => {
  try {
    const { classId } = req.params;

    const address = await prisma.classesAddress.findUnique({
      where: { classId },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    if (!address) {
      return sendError(res, 'Address not found', 404);
    }

    return sendSuccess(res, address, 'Address retrieved successfully');
  } catch (error) {
    console.error('Error fetching address:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Create new address
export const createAddress = async (req: Request, res: Response): Promise<any> => {
  try {
    const {
      classId,
      fullAddress,
      city,
      state,
      postcode,
      country,
      latitude,
      longitude
    } = req.body;

    // Check if class exists
    const classExists = await prisma.classes.findUnique({
      where: { id: classId }
    });

    if (!classExists) {
      return sendError(res, 'Class not found', 404);
    }

    // Check if address already exists for this class
    const existingAddress = await prisma.classesAddress.findUnique({
      where: { classId }
    });

    if (existingAddress) {
      return sendError(res, 'Address already exists for this class', 400);
    }

    const address = await prisma.classesAddress.create({
      data: {
        classId,
        fullAddress,
        city,
        state,
        postcode,
        country,
        latitude,
        longitude
      },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    return sendSuccess(res, address, 'Address created successfully');
  } catch (error) {
    console.error('Error creating address:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Update address
export const updateAddress = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;
    const {
      fullAddress,
      city,
      state,
      postcode,
      country,
      latitude,
      longitude
    } = req.body;

    const address = await prisma.classesAddress.update({
      where: { id },
      data: {
        fullAddress,
        city,
        state,
        postcode,
        country,
        latitude,
        longitude
      },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    return sendSuccess(res, address, 'Address updated successfully');
  } catch (error) {
    console.error('Error updating address:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Delete address
export const deleteAddress = async (req: Request, res: Response): Promise<any> => {
  try {
    const { id } = req.params;

    await prisma.classesAddress.delete({
      where: { id }
    });

    return sendSuccess(res, null, 'Address deleted successfully');
  } catch (error) {
    console.error('Error deleting address:', error);
    return sendError(res, 'Internal server error', 500);
  }
};
