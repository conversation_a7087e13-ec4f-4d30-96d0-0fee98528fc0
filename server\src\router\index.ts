import authAdminRouter from '@/modules/admin/routes/authRoutes';
import authClientRouter from '@/modules/client/routes/authRoutes';
import classesProfileRoutes from '@/modules/client/routes/classesProfileRoutes';
import classesRoutes from '@/modules/client/routes/classesRoutes';
import coinRouter from '@/modules/client/routes/coinRoutes';
import constantRouter from '@/modules/client/routes/constantRoutes';
import adminConstantRouter from '@/modules/admin/routes/constantRoutes';
import examRoutes from '@/modules/admin/routes/examRoutes';
import examApplicationRouter from '@/modules/admin/routes/examApplicationRoutes';
import exportRouter from '@/modules/admin/routes/exportRoutes';
import express from 'express';
import uwhizRankingRouter from '@/modules/client/routes/uwhizRankingRoutes';
import uwhizCertificateRoute from '@/modules/client/routes/uwhizCertificateRoutes';
import studentAuthRouter from '@/modules/client/routes/studentAuthRouter';
import studentProfileRoutes from '@/modules/client/routes/studentProfileRoutes';
import thoughtRouter from '@/modules/client/routes/classesThoughtRoutes';
import classesTestimonialRoutes from '@/modules/client/routes/classesTestimonialRoutes';
import blogRouter from '@/modules/client/routes/blogRoutes/blogRoutes';
import studentWishlistRouter from '@/modules/client/routes/studentWishlistRoutes copy';
import reviewsRouter from "@/modules/client/routes/reviewsRoutes";
import canClassesApplyRouter  from '@/modules/client/routes/canClassesApplyRoutes';
import referralRouter from '@/modules/client/routes/referralRoutes';
import  uwhizPriceRank  from '@/modules/admin/routes/uwhizPriceRankingRoutes';
import getUwhizApplicantRouter from "@/modules/admin/routes/getUwhizApplicantRoutes";
import uwhizCoinDeduction from "@/modules/admin/routes/uwhizCoinDeductionRoutes";
import getUwhizTerminatedStudent from '@/modules/admin/routes/getUwhizTerminatedStudentRoutes';
import examApplicantEmailRoute from '@/modules/client/routes/examApplicantEmailRoutes';
import uwhizStudentDetailRouter from '@/modules/client/routes/uwhizStudentDetailRoutes';
import adminChatRouter from '@/modules/admin/routes/adminChatRouter';
import { router as chatRouter } from '@/socket/socket';
import classViewLogRoutes from '@/modules/client/routes/classViewLogRoutes';
import examMonitoringRouter from '@/modules/client/routes/examMonitoringRoutes';
import uestCoinTransactionRouter from '@/modules/client/routes/uestCoinTransctionRoutes';
import notificationRouter from '@/modules/client/routes/notificationRoutes';
import bankPaymentRouter from '@/modules/client/routes/bankPaymentRoutes';

const router = express.Router();

router.use('/auth-admin', authAdminRouter);
router.use('/exams', examRoutes);
router.use('/exam-applications', examApplicationRouter);
router.use('/auth-client', authClientRouter);
router.use('/classes-profile', classesProfileRoutes);
router.use('/classes', classesRoutes);
router.use('/export', exportRouter);
router.use('/coins', coinRouter);
router.use('/constant', constantRouter);
router.use('/admin/constants', adminConstantRouter);
router.use('/ranking', uwhizRankingRouter);
router.use('/certificate', uwhizCertificateRoute);
router.use('/student', studentAuthRouter);
router.use('/student-profile', studentProfileRoutes);
router.use('/classes-thought', thoughtRouter);
router.use('/testimonials', classesTestimonialRoutes);
router.use('/blogs', blogRouter);
router.use('/student-wishlist', studentWishlistRouter);
router.use('/reviews', reviewsRouter);
router.use('/thoughts', thoughtRouter);
router.use('/can-classes-apply', canClassesApplyRouter);
router.use('/uwhiz-price-rank', uwhizPriceRank);
router.use('/uwhizApplicant', getUwhizApplicantRouter);
router.use('/uwhizCoinDeduction',uwhizCoinDeduction);
router.use('/examApplicantEmail', examApplicantEmailRoute);
router.use('/referral', referralRouter);
router.use('/uwhizStudentData',uwhizStudentDetailRouter);
router.use('/uwhiz-terminated-students',getUwhizTerminatedStudent)
router.use('/class-view-log', classViewLogRoutes);
router.use('/uwhizStudentData',uwhizStudentDetailRouter);
router.use('/uwhizCoinTransction', uestCoinTransactionRouter);;
router.use('/exam-monitoring', examMonitoringRouter);
router.use('/admin-chat', adminChatRouter);
router.use('/chat', chatRouter);
router.use('/notifications', notificationRouter);
router.use('/bank-payment', bankPaymentRouter);

export default router;