<div id="siblings-details" class="content" role="tabpanel" aria-labelledby="siblings-details-trigger">
    <form id="siblings-details-forms">
        <?php echo csrf_field(); ?>
        <input type="hidden" name="student_id" />
        <div class="row" id="clone-fields">
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('sibling_name', 'Siblings Name',['class' => 'form-label']); ?>

                    <?php echo Form::text('sibling_name', null, ['id' => 'sibling_name','placeholder'=>'Enter Sibling Name','class' => 'form-control firstcap']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('sibling_date_of_birth', 'Date Of Birth',['class' => 'form-label']); ?>

                    <?php echo Form::text('sibling_date_of_birth', null, ['id' => 'sibling_date_of_birth','placeholder'=>'Enter Sibling Date Of Birth','class' => 'form-control datepicker']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('studying_std', 'Studying Std',['class' => 'form-label']); ?>

                    <?php echo Form::text('studying_std', null, ['id' => 'studying_std','placeholder'=>'Enter Studying Std','class' => 'form-control']); ?>

                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <?php echo Form::label('school_name', 'School Name',['class' => 'form-label']); ?>

                    <?php echo Form::text('school_name', null, ['id' => 'school_name','placeholder'=>'Enter School Name','class' => 'form-control']); ?>

                </div>
            </div>
        </div>
    </form>
    <button class="btn btn-secondary" onclick="stepper.previous()">Previous</button>
    <button class="btn btn-primary" id="submit-siblings-details">Save & Next</button>
    <button class="btn btn-secondary" onclick="stepper.next()">Skip</button>
</div><?php /**PATH G:\UEST\uest_app\uest-app\classes-dashboard\modules/Admission/resources/views/steps/siblings-details.blade.php ENDPATH**/ ?>