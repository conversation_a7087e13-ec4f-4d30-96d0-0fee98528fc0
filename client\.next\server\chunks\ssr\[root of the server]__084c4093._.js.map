{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/slices/userSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\ninterface User {\r\n  id: string;\r\n  contactNo: string;\r\n  email?: string;\r\n  firstName?: string;\r\n  lastName?: string;\r\n  avatar?: string;\r\n  role?: string;\r\n  profileCompletion?: number;\r\n  className?: string;\r\n  password?: string;\r\n  isVerified?: boolean;\r\n}\r\n\r\n\r\ninterface UserState {\r\n  user: User | null;\r\n  isAuthenticated: boolean;\r\n}\r\n\r\nconst storedUser = typeof window !== 'undefined' ? localStorage.getItem('user') : null;\r\n\r\nconst initialState: UserState = {\r\n  user: storedUser ? JSON.parse(storedUser) : null,\r\n  isAuthenticated: !!storedUser,\r\n};\r\n\r\nconst userSlice = createSlice({\r\n  name: 'user',\r\n  initialState,\r\n  reducers: {\r\n    setUser: (state, action: PayloadAction<{ user: User }>) => {\r\n      state.user = action.payload.user;\r\n      state.isAuthenticated = true;\r\n      localStorage.setItem('user', JSON.stringify(action.payload.user));\r\n    },\r\n    clearUser: (state) => {\r\n      state.user = null;\r\n      state.isAuthenticated = false;\r\n      localStorage.removeItem('user');\r\n    },\r\n  },\r\n});\r\n\r\nexport const { setUser, clearUser } = userSlice.actions;\r\nexport default userSlice.reducer;"], "names": [], "mappings": ";;;;;AAAA;;AAsBA,MAAM,aAAa,6EAA+D;AAElF,MAAM,eAA0B;IAC9B,MAAM,6EAAsC;IAC5C,iBAAiB,CAAC,CAAC;AACrB;AAEA,MAAM,YAAY,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC5B,MAAM;IACN;IACA,UAAU;QACR,SAAS,CAAC,OAAO;YACf,MAAM,IAAI,GAAG,OAAO,OAAO,CAAC,IAAI;YAChC,MAAM,eAAe,GAAG;YACxB,aAAa,OAAO,CAAC,QAAQ,KAAK,SAAS,CAAC,OAAO,OAAO,CAAC,IAAI;QACjE;QACA,WAAW,CAAC;YACV,MAAM,IAAI,GAAG;YACb,MAAM,eAAe,GAAG;YACxB,aAAa,UAAU,CAAC;QAC1B;IACF;AACF;AAEO,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,UAAU,OAAO;uCACxC,UAAU,OAAO", "debugId": null}}, {"offset": {"line": 51, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/slices/formProgressSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\n\r\nexport enum FormId {\r\n  PROFILE = 'about',\r\n  DESCRIPTION = 'description',\r\n  PHOTO_LOGO = 'photo_logo',\r\n  EDUCATION = 'education',\r\n  EXPERIENCE = 'experience',\r\n  CERTIFICATES = 'certificates',\r\n  TUTIONCLASS = 'tution_class',\r\n  ADDRESS = 'address',\r\n}\r\n\r\ninterface FormProgressState {\r\n  completedSteps: number;\r\n  totalSteps: number;\r\n  currentStep: number;\r\n  completedForms: Record<string, boolean>;\r\n}\r\n\r\nconst initialState: FormProgressState = {\r\n  completedSteps: 0,\r\n  totalSteps: 8,\r\n  currentStep: 1,\r\n  completedForms: {\r\n    [FormId.PROFILE]: false,\r\n    [FormId.DESCRIPTION]: false,\r\n    [FormId.PHOTO_LOGO]: false,\r\n    [FormId.EDUCATION]: false,\r\n    [FormId.CERTIFICATES]: false,\r\n    [FormId.EXPERIENCE]: false,\r\n    [FormId.TUTIONCLASS]: false,\r\n    [FormId.ADDRESS]: false,\r\n  },\r\n};\r\n\r\nconst formProgressSlice = createSlice({\r\n  name: 'formProgress',\r\n  initialState,\r\n  reducers: {\r\n    completeForm: (state, action: PayloadAction<FormId>) => {\r\n      const formId = action.payload;\r\n      if (!state.completedForms[formId]) {\r\n        state.completedForms[formId] = true;\r\n        state.completedSteps = Math.min(state.completedSteps + 1, state.totalSteps);\r\n      }\r\n    },\r\n    setCurrentStep: (state, action: PayloadAction<number>) => {\r\n      state.currentStep = action.payload;\r\n    },\r\n  },\r\n});\r\n\r\nexport const { completeForm, setCurrentStep } = formProgressSlice.actions;\r\nexport default formProgressSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;AAAA;;AAEO,IAAA,AAAK,gCAAA;;;;;;;;;WAAA;;AAkBZ,MAAM,eAAkC;IACtC,gBAAgB;IAChB,YAAY;IACZ,aAAa;IACb,gBAAgB;QACd,SAAgB,EAAE;QAClB,eAAoB,EAAE;QACtB,cAAmB,EAAE;QACrB,aAAkB,EAAE;QACpB,gBAAqB,EAAE;QACvB,cAAmB,EAAE;QACrB,gBAAoB,EAAE;QACtB,WAAgB,EAAE;IACpB;AACF;AAEA,MAAM,oBAAoB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACpC,MAAM;IACN;IACA,UAAU;QACR,cAAc,CAAC,OAAO;YACpB,MAAM,SAAS,OAAO,OAAO;YAC7B,IAAI,CAAC,MAAM,cAAc,CAAC,OAAO,EAAE;gBACjC,MAAM,cAAc,CAAC,OAAO,GAAG;gBAC/B,MAAM,cAAc,GAAG,KAAK,GAAG,CAAC,MAAM,cAAc,GAAG,GAAG,MAAM,UAAU;YAC5E;QACF;QACA,gBAAgB,CAAC,OAAO;YACtB,MAAM,WAAW,GAAG,OAAO,OAAO;QACpC;IACF;AACF;AAEO,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,GAAG,kBAAkB,OAAO;uCAC1D,kBAAkB,OAAO", "debugId": null}}, {"offset": {"line": 213, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/axios.ts"], "sourcesContent": ["import axios from 'axios';\r\nimport { toast } from 'sonner';\r\n\r\nconst baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4005/api/v1';\r\nconst baseURL2 = process.env.NEXT_PUBLIC_UWHIZ_API_URL || 'http://localhost:4006';\r\n\r\nconsole.log('Axios baseURL:', baseURL);\r\n\r\nexport const axiosInstance = axios.create({\r\n  baseURL,\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n  },\r\n  withCredentials: true,\r\n});\r\n\r\naxiosInstance.interceptors.request.use(\r\n  (config) => {    \r\n    const serverSelect = config.headers['Server-Select'];\r\n    config.baseURL = serverSelect === 'uwhizServer' ? baseURL2 : baseURL;\r\n\r\n    const studentToken = localStorage.getItem('studentToken');\r\n    if (studentToken) {\r\n      config.headers.Authorization = `Bearer ${studentToken}`;\r\n    }\r\n\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\naxiosInstance.interceptors.response.use(\r\n  (response) => response,\r\n  (error) => {\r\n    if (error.response && error.response.status === 401) {\r\n      toast.error(error.response.data.message || 'Unauthorized');\r\n      localStorage.removeItem('user');\r\n      localStorage.removeItem('studentToken');\r\n      localStorage.removeItem('student_data');\r\n      if (typeof window !== 'undefined') {\r\n          window.location.replace('/?authError=1');\r\n      }\r\n    }\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,UAAU,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AACnD,MAAM,WAAW,6DAAyC;AAE1D,QAAQ,GAAG,CAAC,kBAAkB;AAEvB,MAAM,gBAAgB,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxC;IACA,SAAS;QACP,gBAAgB;IAClB;IACA,iBAAiB;AACnB;AAEA,cAAc,YAAY,CAAC,OAAO,CAAC,GAAG,CACpC,CAAC;IACC,MAAM,eAAe,OAAO,OAAO,CAAC,gBAAgB;IACpD,OAAO,OAAO,GAAG,iBAAiB,gBAAgB,WAAW;IAE7D,MAAM,eAAe,aAAa,OAAO,CAAC;IAC1C,IAAI,cAAc;QAChB,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,cAAc;IACzD;IAEA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,cAAc,YAAY,CAAC,QAAQ,CAAC,GAAG,CACrC,CAAC,WAAa,UACd,CAAC;IACC,IAAI,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,MAAM,KAAK,KAAK;QACnD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI;QAC3C,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,uCAAmC;;QAEnC;IACF;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/thunks/classThunks.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { createAsyncThunk } from '@reduxjs/toolkit';\r\n\r\nexport const fetchClassDetails: any = createAsyncThunk(\r\n  'class/fetchClassDetails',\r\n  async (userId: any, { rejectWithValue }) => {\r\n    try {\r\n      const res = await axiosInstance.get(`/classes/details/${userId}`);\r\n      return res.data;\r\n    } catch (err: any) {\r\n      return rejectWithValue(err.response?.data || 'Fetch failed');\r\n    }\r\n  }\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,MAAM,oBAAyB,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EACnD,2BACA,OAAO,QAAa,EAAE,eAAe,EAAE;IACrC,IAAI;QACF,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,QAAQ;QAChE,OAAO,IAAI,IAAI;IACjB,EAAE,OAAO,KAAU;QACjB,OAAO,gBAAgB,IAAI,QAAQ,EAAE,QAAQ;IAC/C;AACF", "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/slices/classSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { fetchClassDetails } from '../thunks/classThunks';\r\n\r\ninterface ClassState {\r\n  classData: any | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: ClassState = {\r\n  classData: null,\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst classSlice = createSlice({\r\n  name: 'class',\r\n  initialState,\r\n  reducers: {\r\n    setClassData(state, action: PayloadAction) {\r\n      state.classData = action.payload;\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      .addCase(fetchClassDetails.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchClassDetails.fulfilled, (state, action) => {\r\n        state.loading = false;\r\n        state.classData = action.payload;\r\n      })\r\n      .addCase(fetchClassDetails.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { setClassData } = classSlice.actions;\r\nexport default classSlice.reducer;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAQA,MAAM,eAA2B;IAC/B,WAAW;IACX,SAAS;IACT,OAAO;AACT;AAEA,MAAM,aAAa,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IAC7B,MAAM;IACN;IACA,UAAU;QACR,cAAa,KAAK,EAAE,MAAqB;YACvC,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC;IACF;IACA,eAAe,CAAC;QACd,QACG,OAAO,CAAC,qIAAA,CAAA,oBAAiB,CAAC,OAAO,EAAE,CAAC;YACnC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,qIAAA,CAAA,oBAAiB,CAAC,SAAS,EAAE,CAAC,OAAO;YAC5C,MAAM,OAAO,GAAG;YAChB,MAAM,SAAS,GAAG,OAAO,OAAO;QAClC,GACC,OAAO,CAAC,qIAAA,CAAA,oBAAiB,CAAC,QAAQ,EAAE,CAAC,OAAO;YAC3C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,YAAY,EAAE,GAAG,WAAW,OAAO;uCACnC,WAAW,OAAO", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/thunks/studentProfileThunks.ts"], "sourcesContent": ["import { axiosInstance } from '@/lib/axios';\r\nimport { createAsyncThunk } from '@reduxjs/toolkit';\r\n\r\nexport const fetchStudentProfile = createAsyncThunk(\r\n  'studentProfile/fetchStudentProfile',\r\n  async (_, { rejectWithValue }) => {\r\n    try {\r\n      const studentToken = localStorage.getItem('studentToken');\r\n      if (!studentToken) {\r\n        return rejectWithValue('No authentication token found');\r\n      }\r\n\r\n      const res = await axiosInstance.get('/student-profile/all-data', {\r\n        headers: {\r\n          'Authorization': `Bearer ${studentToken}`\r\n        }\r\n      });\r\n\r\n      if (res.data && typeof res.data === 'object') {\r\n        // Check if the response has a data property (API wrapper format)\r\n        if (res.data.success !== undefined && res.data.data !== undefined) {\r\n          return res.data.data;\r\n        }\r\n        return res.data;\r\n      }\r\n\r\n      return null;\r\n    } catch (err: any) {\r\n      if (err.response?.status === 404) {\r\n        return null;\r\n      }\r\n\r\n      const errorMessage = err.response?.data?.message || 'Failed to fetch student data';\r\n      return rejectWithValue(errorMessage);\r\n    }\r\n  }\r\n);\r\n\r\nexport const updateStudentProfile = createAsyncThunk(\r\n  'studentProfile/updateStudentProfile',\r\n  async (jsonData: any, { rejectWithValue }) => {\r\n    try {\r\n      const studentToken = localStorage.getItem('studentToken');\r\n      if (!studentToken) {\r\n        return rejectWithValue('No authentication token found');\r\n      }\r\n\r\n      const url = '/student-profile/combined';\r\n      const method = 'put';\r\n\r\n      const res = await axiosInstance({\r\n        method,\r\n        url,\r\n        data: jsonData,\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n          'Authorization': `Bearer ${studentToken}`\r\n        },\r\n      });\r\n\r\n      if (res.data && typeof res.data === 'object') {\r\n        // Check if the response has a data property (API wrapper format)\r\n        if (res.data.success !== undefined && res.data.data !== undefined) {\r\n          return res.data.data;\r\n        }\r\n        return res.data;\r\n      }\r\n\r\n      return null;\r\n    } catch (err: any) {\r\n      const errorMsg = err.response?.data?.message || 'Failed to update student profile';\r\n      return rejectWithValue(errorMsg);\r\n    }\r\n  }\r\n);"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,MAAM,sBAAsB,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EAChD,sCACA,OAAO,GAAG,EAAE,eAAe,EAAE;IAC3B,IAAI;QACF,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,CAAC,cAAc;YACjB,OAAO,gBAAgB;QACzB;QAEA,MAAM,MAAM,MAAM,mHAAA,CAAA,gBAAa,CAAC,GAAG,CAAC,6BAA6B;YAC/D,SAAS;gBACP,iBAAiB,CAAC,OAAO,EAAE,cAAc;YAC3C;QACF;QAEA,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,UAAU;YAC5C,iEAAiE;YACjE,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW;gBACjE,OAAO,IAAI,IAAI,CAAC,IAAI;YACtB;YACA,OAAO,IAAI,IAAI;QACjB;QAEA,OAAO;IACT,EAAE,OAAO,KAAU;QACjB,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;QACpD,OAAO,gBAAgB;IACzB;AACF;AAGK,MAAM,uBAAuB,CAAA,GAAA,2LAAA,CAAA,mBAAgB,AAAD,EACjD,uCACA,OAAO,UAAe,EAAE,eAAe,EAAE;IACvC,IAAI;QACF,MAAM,eAAe,aAAa,OAAO,CAAC;QAC1C,IAAI,CAAC,cAAc;YACjB,OAAO,gBAAgB;QACzB;QAEA,MAAM,MAAM;QACZ,MAAM,SAAS;QAEf,MAAM,MAAM,MAAM,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE;YAC9B;YACA;YACA,MAAM;YACN,SAAS;gBACP,gBAAgB;gBAChB,iBAAiB,CAAC,OAAO,EAAE,cAAc;YAC3C;QACF;QAEA,IAAI,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,KAAK,UAAU;YAC5C,iEAAiE;YACjE,IAAI,IAAI,IAAI,CAAC,OAAO,KAAK,aAAa,IAAI,IAAI,CAAC,IAAI,KAAK,WAAW;gBACjE,OAAO,IAAI,IAAI,CAAC,IAAI;YACtB;YACA,OAAO,IAAI,IAAI;QACjB;QAEA,OAAO;IACT,EAAE,OAAO,KAAU;QACjB,MAAM,WAAW,IAAI,QAAQ,EAAE,MAAM,WAAW;QAChD,OAAO,gBAAgB;IACzB;AACF", "debugId": null}}, {"offset": {"line": 393, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/slices/studentProfileSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\r\nimport { fetchStudentProfile, updateStudentProfile } from '../thunks/studentProfileThunks';\r\n\r\nexport interface StudentData {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  contact?: string;\r\n  isVerified?: boolean;\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n}\r\n\r\nexport interface ProfileData {\r\n  id?: string;\r\n  studentId?: string;\r\n  medium?: string;\r\n  classroom?: string;\r\n  birthday?: string | Date;\r\n  school?: string;\r\n  address?: string;\r\n  photo?: string;\r\n  documentUrl?: string;\r\n  status?: 'PENDING' | 'APPROVED' | 'REJECTED';\r\n  createdAt?: string;\r\n  updatedAt?: string;\r\n  student?: StudentData;\r\n}\r\n\r\nexport interface ClassroomOption {\r\n  id: number;\r\n  value: string;\r\n}\r\n\r\nexport interface StudentProfile {\r\n  profile?: ProfileData;\r\n  classroomOptions?: ClassroomOption[];\r\n  coins?: number;\r\n}\r\n\r\ninterface StudentProfileState {\r\n  profileData: StudentProfile | null;\r\n  loading: boolean;\r\n  error: string | null;\r\n}\r\n\r\nconst initialState: StudentProfileState = {\r\n  profileData: null,\r\n  loading: false,\r\n  error: null,\r\n};\r\n\r\nconst studentProfileSlice = createSlice({\r\n  name: 'studentProfile',\r\n  initialState,\r\n  reducers: {\r\n    setStudentProfileData(state, action: PayloadAction<StudentProfile>) {\r\n      state.profileData = action.payload;\r\n      // Persist to localStorage for photo data\r\n      if (typeof window !== 'undefined' && action.payload?.profile?.photo) {\r\n        try {\r\n          localStorage.setItem('student_profile_photo', action.payload.profile.photo);\r\n        } catch (error) {\r\n          console.error('Failed to persist photo to localStorage:', error);\r\n        }\r\n      }\r\n    },\r\n    updateProfilePhoto(state, action: PayloadAction<string | undefined>) {\r\n      if (state.profileData?.profile) {\r\n        state.profileData.profile.photo = action.payload;\r\n        // Persist to localStorage\r\n        if (typeof window !== 'undefined') {\r\n          try {\r\n            if (action.payload) {\r\n              localStorage.setItem('student_profile_photo', action.payload);\r\n            } else {\r\n              localStorage.removeItem('student_profile_photo');\r\n            }\r\n          } catch (error) {\r\n            console.error('Failed to persist photo to localStorage:', error);\r\n          }\r\n        }\r\n      }\r\n    },\r\n    clearStudentProfileData(state) {\r\n      state.profileData = null;\r\n      state.loading = false;\r\n      state.error = null;\r\n      // Clear persisted photo\r\n      if (typeof window !== 'undefined') {\r\n        try {\r\n          localStorage.removeItem('student_profile_photo');\r\n        } catch (error) {\r\n          console.error('Failed to clear photo from localStorage:', error);\r\n        }\r\n      }\r\n    },\r\n  },\r\n  extraReducers: (builder) => {\r\n    builder\r\n      // Fetch student profile\r\n      .addCase(fetchStudentProfile.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(fetchStudentProfile.fulfilled, (state, action) => {\r\n        state.loading = false;\r\n        // Ensure we're handling the payload correctly\r\n        if (action.payload) {\r\n          state.profileData = action.payload;\r\n        }\r\n      })\r\n      .addCase(fetchStudentProfile.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      })\r\n\r\n      // Update student profile\r\n      .addCase(updateStudentProfile.pending, (state) => {\r\n        state.loading = true;\r\n        state.error = null;\r\n      })\r\n      .addCase(updateStudentProfile.fulfilled, (state, action) => {\r\n        state.loading = false;\r\n        // Ensure we're handling the payload correctly\r\n        if (action.payload) {\r\n          state.profileData = action.payload;\r\n        }\r\n      })\r\n      .addCase(updateStudentProfile.rejected, (state, action) => {\r\n        state.loading = false;\r\n        state.error = action.payload as string;\r\n      });\r\n  },\r\n});\r\n\r\nexport const { setStudentProfileData, updateProfilePhoto, clearStudentProfileData } = studentProfileSlice.actions;\r\nexport default studentProfileSlice.reducer;\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AA8CA,MAAM,eAAoC;IACxC,aAAa;IACb,SAAS;IACT,OAAO;AACT;AAEA,MAAM,sBAAsB,CAAA,GAAA,2LAAA,CAAA,cAAW,AAAD,EAAE;IACtC,MAAM;IACN;IACA,UAAU;QACR,uBAAsB,KAAK,EAAE,MAAqC;YAChE,MAAM,WAAW,GAAG,OAAO,OAAO;YAClC,yCAAyC;YACzC,IAAI,gBAAkB,eAAe,OAAO,OAAO,EAAE,SAAS,OAAO;;YAMrE;QACF;QACA,oBAAmB,KAAK,EAAE,MAAyC;YACjE,IAAI,MAAM,WAAW,EAAE,SAAS;gBAC9B,MAAM,WAAW,CAAC,OAAO,CAAC,KAAK,GAAG,OAAO,OAAO;gBAChD,0BAA0B;gBAC1B,uCAAmC;;gBAUnC;YACF;QACF;QACA,yBAAwB,KAAK;YAC3B,MAAM,WAAW,GAAG;YACpB,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;YACd,wBAAwB;YACxB,uCAAmC;;YAMnC;QACF;IACF;IACA,eAAe,CAAC;QACd,OACE,wBAAwB;SACvB,OAAO,CAAC,8IAAA,CAAA,sBAAmB,CAAC,OAAO,EAAE,CAAC;YACrC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,8IAAA,CAAA,sBAAmB,CAAC,SAAS,EAAE,CAAC,OAAO;YAC9C,MAAM,OAAO,GAAG;YAChB,8CAA8C;YAC9C,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW,GAAG,OAAO,OAAO;YACpC;QACF,GACC,OAAO,CAAC,8IAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE,CAAC,OAAO;YAC7C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B,EAEA,yBAAyB;SACxB,OAAO,CAAC,8IAAA,CAAA,uBAAoB,CAAC,OAAO,EAAE,CAAC;YACtC,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG;QAChB,GACC,OAAO,CAAC,8IAAA,CAAA,uBAAoB,CAAC,SAAS,EAAE,CAAC,OAAO;YAC/C,MAAM,OAAO,GAAG;YAChB,8CAA8C;YAC9C,IAAI,OAAO,OAAO,EAAE;gBAClB,MAAM,WAAW,GAAG,OAAO,OAAO;YACpC;QACF,GACC,OAAO,CAAC,8IAAA,CAAA,uBAAoB,CAAC,QAAQ,EAAE,CAAC,OAAO;YAC9C,MAAM,OAAO,GAAG;YAChB,MAAM,KAAK,GAAG,OAAO,OAAO;QAC9B;IACJ;AACF;AAEO,MAAM,EAAE,qBAAqB,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,GAAG,oBAAoB,OAAO;uCAClG,oBAAoB,OAAO", "debugId": null}}, {"offset": {"line": 476, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/index.ts"], "sourcesContent": ["import { configureStore } from '@reduxjs/toolkit';\r\nimport userReducer from './slices/userSlice';\r\nimport formProgressReducer from './slices/formProgressSlice';\r\nimport classReducer from './slices/classSlice';\r\nimport studentProfileReducer from './slices/studentProfileSlice';\r\n\r\nexport const store = configureStore({\r\n  reducer: {\r\n    user: userReducer,\r\n    formProgress: formProgressReducer,\r\n    class: classReducer,\r\n    studentProfile: studentProfileReducer,\r\n  },\r\n});\r\n\r\nexport type RootState = ReturnType<typeof store.getState>;\r\nexport type AppDispatch = typeof store.dispatch;\r\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AAEO,MAAM,QAAQ,CAAA,GAAA,2LAAA,CAAA,iBAAc,AAAD,EAAE;IAClC,SAAS;QACP,MAAM,mIAAA,CAAA,UAAW;QACjB,cAAc,2IAAA,CAAA,UAAmB;QACjC,OAAO,oIAAA,CAAA,UAAY;QACnB,gBAAgB,6IAAA,CAAA,UAAqB;IACvC;AACF", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/store/provider.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Provider } from 'react-redux';\r\nimport { store } from './index';\r\n\r\nexport function ReduxProvider({ children }: { children: React.ReactNode }) {\r\n  return <Provider store={store}>{children}</Provider>;\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKO,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,qBAAO,8OAAC,yJAAA,CAAA,WAAQ;QAAC,OAAO,qHAAA,CAAA,QAAK;kBAAG;;;;;;AAClC", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/components/ui/sonner.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useTheme } from 'next-themes';\r\nimport { Toaster as Sonner, ToasterProps } from 'sonner';\r\n\r\nconst Toaster = ({ ...props }: ToasterProps) => {\r\n  const { theme = 'system' } = useTheme();\r\n\r\n  return (\r\n    <Sonner\r\n      theme={theme as ToasterProps['theme']}\r\n      className=\"toaster group\"\r\n      style={\r\n        {\r\n          '--normal-bg': 'var(--popover)',\r\n          '--normal-text': 'var(--popover-foreground)',\r\n          '--normal-border': 'var(--border)',\r\n        } as React.CSSProperties\r\n      }\r\n      {...props}\r\n    />\r\n  );\r\n};\r\n\r\nexport { Toaster };\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 587, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/lib/gtag.ts"], "sourcesContent": ["export const GA_TRACKING_ID = 'G-N06ZRQXN1Y';\r\n\r\nexport const pageview = (url: string) => {\r\n  (window as any).gtag('config', GA_TRACKING_ID, {\r\n    page_path: url,\r\n  });\r\n};\r\n\r\nexport const event = ({\r\n  action,\r\n  category,\r\n  label,\r\n  value,\r\n}: {\r\n  action: string;\r\n  category: string;\r\n  label: string;\r\n  value: number;\r\n}) => {\r\n  (window as any).gtag('event', action, {\r\n    event_category: category,\r\n    event_label: label,\r\n    value: value,\r\n  });\r\n};"], "names": [], "mappings": ";;;;;AAAO,MAAM,iBAAiB;AAEvB,MAAM,WAAW,CAAC;IACtB,OAAe,IAAI,CAAC,UAAU,gBAAgB;QAC7C,WAAW;IACb;AACF;AAEO,MAAM,QAAQ,CAAC,EACpB,MAAM,EACN,QAAQ,EACR,KAAK,EACL,KAAK,EAMN;IACE,OAAe,IAAI,CAAC,SAAS,QAAQ;QACpC,gBAAgB;QAChB,aAAa;QACb,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file://G%3A/UEST/uest_app/uest-app/client/src/hooks/AnalyticsProvider.tsx"], "sourcesContent": ["'use client';\r\nimport { usePathname, useSearchParams } from 'next/navigation';\r\nimport { useEffect } from 'react';\r\nimport * as gtag from '@/lib/gtag';\r\n\r\nexport default function AnalyticsProvider() {\r\n  const pathname = usePathname();\r\n  const searchParams = useSearchParams();\r\n\r\n  useEffect(() => {\r\n    const url = pathname + searchParams.toString();\r\n    gtag.pageview(url);\r\n  }, [pathname, searchParams]);\r\n\r\n  return null;\r\n}"], "names": [], "mappings": ";;;AACA;AACA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,MAAM,WAAW,aAAa,QAAQ;QAC5C,CAAA,GAAA,kHAAA,CAAA,WAAa,AAAD,EAAE;IAChB,GAAG;QAAC;QAAU;KAAa;IAE3B,OAAO;AACT", "debugId": null}}]}