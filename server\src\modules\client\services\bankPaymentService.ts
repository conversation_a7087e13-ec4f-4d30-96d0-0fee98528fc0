import prisma from '@/config/prismaClient';

export interface BankPaymentData {
  bankName: string;
  accountNumber: string;
  reAccountNumber: string;
  ifscCode: string;
  accountHolderName: string;
  branchName: string;
}

// Create new bank payment details
export const createBankPaymentDetails = async (classId: string, data: BankPaymentData) => {
  // Convert all text fields to uppercase
  const upperCaseData = {
    bankName: data.bankName.toUpperCase(),
    accountNumber: data.accountNumber.toUpperCase(),
    reAccountNumber: data.reAccountNumber.toUpperCase(),
    ifscCode: data.ifscCode.toUpperCase(),
    accountHolderName: data.accountHolderName.toUpperCase(),
    branchName: data.branchName.toUpperCase()
  };

  return await prisma.bankPayment.create({
    data: {
      classId,
      ...upperCaseData
    }
  });
};

// Update existing bank payment details
export const updateBankPaymentDetails = async (id: string, data: BankPaymentData) => {
  // Convert all text fields to uppercase
  const upperCaseData = {
    bankName: data.bankName.toUpperCase(),
    accountNumber: data.accountNumber.toUpperCase(),
    reAccountNumber: data.reAccountNumber.toUpperCase(),
    ifscCode: data.ifscCode.toUpperCase(),
    accountHolderName: data.accountHolderName.toUpperCase(),
    branchName: data.branchName.toUpperCase()
  };

  return await prisma.bankPayment.update({
    where: { id },
    data: {
      ...upperCaseData,
      updatedAt: new Date()
    }
  });
};

// Find bank payment details by class ID
export const findBankPaymentByClassId = async (classId: string) => {
  return await prisma.bankPayment.findFirst({
    where: { classId },
    include: {
      class: {
        select: {
          id: true,
          firstName: true,
          lastName: true,
          className: true,
          email: true
        }
      }
    }
  });
};

// Get all bank payment details with pagination and search
export const getAllBankPaymentDetails = async (
  page: number = 1,
  limit: number = 10,
  search: string = ''
) => {
  const skip = (page - 1) * limit;

  const whereClause = search ? {
    OR: [
      { bankName: { contains: search, mode: 'insensitive' as const } },
      { accountHolderName: { contains: search, mode: 'insensitive' as const } },
      { class: {
        OR: [
          { firstName: { contains: search, mode: 'insensitive' as const } },
          { lastName: { contains: search, mode: 'insensitive' as const } },
          { className: { contains: search, mode: 'insensitive' as const } }
        ]
      }}
    ]
  } : {};

  const [bankPayments, total] = await Promise.all([
    prisma.bankPayment.findMany({
      where: whereClause,
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true,
            contactNo: true
          }
        }
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.bankPayment.count({ where: whereClause })
  ]);

  return {
    bankPayments,
    pagination: {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  };
};

// Find bank payment by ID and class ID
export const findBankPaymentByIdAndClassId = async (id: string, classId: string) => {
  return await prisma.bankPayment.findFirst({
    where: {
      id,
      classId
    }
  });
};

// Delete bank payment details
export const deleteBankPaymentById = async (id: string) => {
  return await prisma.bankPayment.delete({
    where: { id }
  });
};

// Get payment history for a class with pagination
export const getPaymentHistoryByClassId = async (
  classId: string,
  page: number = 1,
  limit: number = 10
) => {
  const skip = (page - 1) * limit;

  const [payments, total] = await Promise.all([
    prisma.bankPayment.findMany({
      where: { classId },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    }),
    prisma.bankPayment.count({ where: { classId } })
  ]);

  return {
    payments,
    pagination: {
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  };
};

// Check if bank payment exists for class
export const checkBankPaymentExists = async (classId: string) => {
  const count = await prisma.bankPayment.count({
    where: { classId }
  });
  return count > 0;
};

