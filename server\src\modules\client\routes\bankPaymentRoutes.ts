import express from 'express';
import {
  createBankPayment,
  getBankPaymentDetails
} from '../controllers/bankPaymentController';
import { authClientMiddleware } from '@/middlewares/clientAuth';

const bankPaymentRouter = express.Router();

// Class routes (require class authentication)
bankPaymentRouter.post('/create', authClientMiddleware, createBankPayment);
bankPaymentRouter.get('/details', authClientMiddleware, getBankPaymentDetails);

export default bankPaymentRouter;