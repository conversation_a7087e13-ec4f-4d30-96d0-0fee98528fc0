import express from 'express';
import {
  createBankPayment,
  getBankPaymentDetails,
  updateBankPayment
} from '../controllers/bankPaymentController';
import { authClientMiddleware } from '@/middlewares/clientAuth';

const bankPaymentRouter = express.Router();

// Class routes (require class authentication)
bankPaymentRouter.post('/create', authClientMiddleware, createBankPayment);
bankPaymentRouter.put('/update/:id', authClientMiddleware, updateBankPayment);
bankPaymentRouter.get('/details', authClientMiddleware, getBankPaymentDetails);

export default bankPaymentRouter;