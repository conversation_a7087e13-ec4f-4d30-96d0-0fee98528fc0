import express from 'express';
import {
  createBankPayment,
  getBankPaymentDetails,
  getAllBankPayments,
  deleteBankPayment,
  getPaymentHistory
} from '../controllers/bankPaymentController';
import { authClientMiddleware } from '@/middlewares/clientAuth';
import { authMiddleware } from '@/middlewares/adminAuth';

const bankPaymentRouter = express.Router();

// Class routes (require class authentication)
bankPaymentRouter.post('/create', authClientMiddleware, createBankPayment);
bankPaymentRouter.get('/details', authClientMiddleware, getBankPaymentDetails);
bankPaymentRouter.delete('/:id', authClientMiddleware, deleteBankPayment);
bankPaymentRouter.get('/history', authClientMiddleware, getPaymentHistory);

// Admin routes (require admin authentication)
bankPaymentRouter.get('/admin/all', authMiddleware, getAllBankPayments);

export default bankPaymentRouter;