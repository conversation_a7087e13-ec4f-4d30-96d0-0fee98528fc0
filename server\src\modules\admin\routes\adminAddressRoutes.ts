import express from 'express';
import {
  getAllAddresses,
  getAddressByClassId,
  createAddress,
  updateAddress,
  deleteAddress
} from '../controllers/adminAddressController';
import { authMiddleware } from '@/middlewares/adminAuth';

const adminAddressRouter = express.Router();

// Admin routes for address management
adminAddressRouter.get('/', authMiddleware, getAllAddresses);
adminAddressRouter.get('/:classId', authMiddleware, getAddressByClassId);
adminAddressRouter.post('/', authMiddleware, createAddress);
adminAddressRouter.put('/:id', authMiddleware, updateAddress);
adminAddressRouter.delete('/:id', authMiddleware, deleteAddress);

export default adminAddressRouter;
