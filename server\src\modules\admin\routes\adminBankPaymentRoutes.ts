import express from 'express';
import {
  getAllBankPayments,
  getBankPaymentsByClassId,
  createBankPayment,
  updateBankPayment,
  deleteBankPayment
} from '../controllers/adminBankPaymentController';
import { authMiddleware } from '@/middlewares/adminAuth';

const adminBankPaymentRouter = express.Router();

// Admin routes for bank payment management
adminBankPaymentRouter.get('/', authMiddleware, getAllBankPayments);
adminBankPaymentRouter.get('/:classId', authMiddleware, getBankPaymentsByClassId);
adminBankPaymentRouter.post('/', authMiddleware, createBankPayment);
adminBankPaymentRouter.put('/:id', authMiddleware, updateBankPayment);
adminBankPaymentRouter.delete('/:id', authMiddleware, deleteBankPayment);

export default adminBankPaymentRouter;
