'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { Wallet, History, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { axiosInstance } from '@/lib/axios';
import { BankPaymentData } from '@/lib/types';
import { toast } from 'sonner';

const bankPaymentSchema = z.object({
  bankName: z.string()
    .min(2, 'Bank name must be at least 2 characters')
    .max(100, 'Bank name cannot exceed 100 characters')
    .regex(/^[A-Za-z\s]+$/, 'Bank name can only contain letters and spaces'),

  accountNumber: z.string()
    .regex(/^[0-9]{9,18}$/, 'Account number must be 9-18 digits'),

  reAccountNumber: z.string()
    .min(1, 'Please re-enter account number'),

  ifscCode: z.string()
    .regex(/^[A-Z]{4}0[A-Z0-9]{6}$/, 'Invalid IFSC code format (e.g., SBIN0001234)'),

  accountHolderName: z.string()
    .min(2, 'Account holder name must be at least 2 characters')
    .max(100, 'Account holder name cannot exceed 100 characters')
    .regex(/^[A-Za-z\s]+$/, 'Account holder name can only contain letters and spaces'),

  branchName: z.string()
    .min(2, 'Branch name must be at least 2 characters')
    .max(100, 'Branch name cannot exceed 100 characters')
}).refine((data) => data.accountNumber === data.reAccountNumber, {
  message: 'Account numbers do not match',
  path: ['reAccountNumber']
});

type BankPaymentFormValues = z.infer<typeof bankPaymentSchema>;

const PaymentPage = () => {
  const [loading, setLoading] = useState(false);
  const [existingPaymentId, setExistingPaymentId] = useState<string | null>(null);

  const form = useForm<BankPaymentFormValues>({
    resolver: zodResolver(bankPaymentSchema),
    defaultValues: {
      bankName: '',
      accountNumber: '',
      reAccountNumber: '',
      ifscCode: '',
      accountHolderName: '',
      branchName: ''
    },
    mode: 'onChange'
  });

  useEffect(() => {
    fetchBankPaymentDetails();             
  }, []);

  const fetchBankPaymentDetails = async () => {
    try {
      const response = await axiosInstance.get('/bank-payment/details');

      if (response.data.success && response.data.data) {
        const data = response.data.data;
        setExistingPaymentId(data.id);
        form.reset({
          bankName: data.bankName || '',
          accountNumber: data.accountNumber || '',
          reAccountNumber: data.reAccountNumber || '',
          ifscCode: data.ifscCode || '',
          accountHolderName: data.accountHolderName || '',
          branchName: data.branchName || ''
        });
      } else {
        setExistingPaymentId(null);
      }
    } catch (error) {
      console.error('Error fetching bank payment details:', error);
      setExistingPaymentId(null);
    }
  };

  const onSubmit = async (data: BankPaymentFormValues) => {
    setLoading(true);
    try {
      let response;

      if (existingPaymentId) {
        response = await axiosInstance.put(`/bank-payment/update/${existingPaymentId}`, data);
        toast.success('Bank payment details updated successfully!');
      } else {
        response = await axiosInstance.post('/bank-payment/create', data);
        toast.success('Bank payment details created successfully!');

        if (response.data.success && response.data.data?.id) {
          setExistingPaymentId(response.data.data.id);
        }
      }
    } catch (error: any) {
      console.error('Error saving bank payment details:', error);
      toast.error(error.response?.data?.message || 'Error saving bank payment details');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Header />
      <div className="container mx-auto px-16 py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Portal</h1>
            <p className="text-muted-foreground">
              Manage your payments and view transaction history
            </p>
          </div>
        </div>

        <Tabs defaultValue="payment" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Account Details
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Payment History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-orange-600" />
                  Bank Payment Details
                </CardTitle>
                <CardDescription>
                  Enter your bank details for payment processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Form {...form}>
                  <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <div className="grid gap-4 md:grid-cols-2">

                      <FormField
                        control={form.control}
                        name="bankName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Bank Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter bank name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="accountNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Account Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter account number"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="reAccountNumber"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Re Account Number</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Re-enter account number"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="ifscCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>IFSC Code</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter IFSC code"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="accountHolderName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Account Holder Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter account holder name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="branchName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Branch Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter branch name"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="mt-6">
                      <Button
                        type="submit"
                        className="w-full bg-customOrange hover:bg-orange-600 text-white"
                        disabled={loading}
                      >
                        {loading ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            {existingPaymentId ? 'Updating...' : 'Saving...'}
                          </>
                        ) : (
                          existingPaymentId ? 'Update Payment Details' : 'Submit Payment Details'
                        )}
                      </Button>
                    </div>
                  </form>
                </Form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-orange-600" />
                  Payment History
                </CardTitle>
                <CardDescription>
                  View all your payment transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">Date</th>
                        <th className="text-left p-3 font-medium">Bank Name</th>
                        <th className="text-left p-3 font-medium">Account Number</th>
                        <th className="text-left p-3 font-medium">Account Holder</th>
                        <th className="text-left p-3 font-medium">Status</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr className="border-b">
                        <td className="p-3 text-gray-500" colSpan={5}>
                          No payment history found
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </>
  );
};

export default PaymentPage;