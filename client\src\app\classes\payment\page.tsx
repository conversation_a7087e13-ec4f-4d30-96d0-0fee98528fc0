'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { Wallet, History } from 'lucide-react';
import { Input } from '@/components/ui/input';

const PaymentPage = () => {
  const [amount, setAmount] = useState('');

  return (
    <>
      <Header />
      <div className="container mx-auto px-16 py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Portal</h1>
            <p className="text-muted-foreground">
              Manage your payments and view transaction history
            </p>
          </div>
        </div>

        <Tabs defaultValue="payment" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Account Details
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Payment History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-orange-600" />
                  Bank Payment Details
                </CardTitle>
                <CardDescription>
                  Enter your bank details for payment processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">

                  <div className="space-y-2">
                    <label htmlFor="bankName" className="text-sm font-medium">
                      Bank Name
                    </label>
                    <Input
                      id="bankName"
                      type="text"
                      placeholder="Enter bank name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="amount" className="text-sm font-medium">
                      Account Number
                    </label>
                    <Input
                      id="amount"
                      type="number"
                      placeholder="Enter amount"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="accountNumber" className="text-sm font-medium">
                      Re Account Number
                    </label>
                    <Input
                      id="accountNumber"
                      type="text"
                      placeholder="Enter account number"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="ifscCode" className="text-sm font-medium">
                      IFSC Code
                    </label>
                    <Input
                      id="ifscCode"
                      type="text"
                      placeholder="Enter IFSC code"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="accountHolder" className="text-sm font-medium">
                      Account Holder Name
                    </label>
                    <Input
                      id="accountHolder"
                      type="text"
                      placeholder="Enter account holder name"
                    />
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="branchName" className="text-sm font-medium">
                      Branch Name
                    </label>
                    <Input
                      id="branchName"
                      type="text"
                      placeholder="Enter branch name"
                    />
                  </div>
                </div>

                <div className="mt-6">
                  <Button className="w-full bg-customOrange hover:bg-orange-600 text-white">
                    Submit Payment Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-orange-600" />
                  Payment History
                </CardTitle>
                <CardDescription>
                  View all your payment transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-3 font-medium">Date</th>
                        <th className="text-left p-3 font-medium">Payment ID</th>
                        <th className="text-left p-3 font-medium">Amount</th>
                        <th className="text-left p-3 font-medium">Status</th>
                        <th className="text-left p-3 font-medium">Method</th>
                      </tr>
                    </thead>
                    <tbody>
                      <p>No payment history found</p>
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </>
  );
};

export default PaymentPage;