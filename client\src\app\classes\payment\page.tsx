'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Footer from '@/app-components/Footer';
import Header from '@/app-components/Header';
import { Wallet, History, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import axios from 'axios';

interface BankPaymentData {
  id?: string;
  bankName: string;
  accountNumber: string;
  reAccountNumber: string;
  ifscCode: string;
  accountHolderName: string;
  branchName: string;
  createdAt?: string;
  updatedAt?: string;
}

interface PaymentHistoryItem {
  id: string;
  bankName: string;
  accountNumber: string;
  accountHolderName: string;
  createdAt: string;
}

const PaymentPage = () => {
  const [formData, setFormData] = useState<BankPaymentData>({
    bankName: '',
    accountNumber: '',
    reAccountNumber: '',
    ifscCode: '',
    accountHolderName: '',
    branchName: ''
  });
  const [paymentHistory, setPaymentHistory] = useState<PaymentHistoryItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingHistory, setLoadingHistory] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch existing bank payment details on component mount
  useEffect(() => {
    fetchBankPaymentDetails();
    fetchPaymentHistory();
  }, []);

  const fetchBankPaymentDetails = async () => {
    try {
      const response = await axios.get('/api/bank-payment/details', {
        withCredentials: true
      });

      if (response.data.success && response.data.data) {
        setFormData(response.data.data);
      }
    } catch (error) {
      console.error('Error fetching bank payment details:', error);
    }
  };

  const fetchPaymentHistory = async () => {
    setLoadingHistory(true);
    try {
      const response = await axios.get('/api/bank-payment/history', {
        withCredentials: true
      });

      if (response.data.success) {
        setPaymentHistory(response.data.data.payments || []);
      }
    } catch (error) {
      console.error('Error fetching payment history:', error);
    } finally {
      setLoadingHistory(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.bankName.trim()) {
      newErrors.bankName = 'Bank name is required';
    }
    if (!formData.accountNumber.trim()) {
      newErrors.accountNumber = 'Account number is required';
    }
    if (!formData.reAccountNumber.trim()) {
      newErrors.reAccountNumber = 'Please re-enter account number';
    }
    if (formData.accountNumber !== formData.reAccountNumber) {
      newErrors.reAccountNumber = 'Account numbers do not match';
    }
    if (!formData.ifscCode.trim()) {
      newErrors.ifscCode = 'IFSC code is required';
    }
    if (!formData.accountHolderName.trim()) {
      newErrors.accountHolderName = 'Account holder name is required';
    }
    if (!formData.branchName.trim()) {
      newErrors.branchName = 'Branch name is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      const response = await axios.post('/api/bank-payment/create', formData, {
        withCredentials: true
      });

      if (response.data.success) {
        alert('Bank payment details saved successfully!');
        fetchPaymentHistory(); // Refresh payment history
      }
    } catch (error: any) {
      console.error('Error saving bank payment details:', error);
      alert(error.response?.data?.message || 'Error saving bank payment details');
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Header />
      <div className="container mx-auto px-16 py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Payment Portal</h1>
            <p className="text-muted-foreground">
              Manage your payments and view transaction history
            </p>
          </div>
        </div>

        <Tabs defaultValue="payment" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="payment" className="flex items-center gap-2">
              <Wallet className="h-4 w-4" />
              Account Details
            </TabsTrigger>
            <TabsTrigger value="history" className="flex items-center gap-2">
              <History className="h-4 w-4" />
              Payment History
            </TabsTrigger>
          </TabsList>

          <TabsContent value="payment" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Wallet className="h-5 w-5 text-orange-600" />
                  Bank Payment Details
                </CardTitle>
                <CardDescription>
                  Enter your bank details for payment processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit}>
                  <div className="grid gap-4 md:grid-cols-2">

                    <div className="space-y-2">
                      <label htmlFor="bankName" className="text-sm font-medium">
                        Bank Name
                      </label>
                      <Input
                        id="bankName"
                        name="bankName"
                        type="text"
                        placeholder="Enter bank name"
                        value={formData.bankName}
                        onChange={handleInputChange}
                        className={errors.bankName ? 'border-red-500' : ''}
                      />
                      {errors.bankName && (
                        <p className="text-sm text-red-500">{errors.bankName}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="accountNumber" className="text-sm font-medium">
                        Account Number
                      </label>
                      <Input
                        id="accountNumber"
                        name="accountNumber"
                        type="text"
                        placeholder="Enter account number"
                        value={formData.accountNumber}
                        onChange={handleInputChange}
                        className={errors.accountNumber ? 'border-red-500' : ''}
                      />
                      {errors.accountNumber && (
                        <p className="text-sm text-red-500">{errors.accountNumber}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="reAccountNumber" className="text-sm font-medium">
                        Re Account Number
                      </label>
                      <Input
                        id="reAccountNumber"
                        name="reAccountNumber"
                        type="text"
                        placeholder="Re-enter account number"
                        value={formData.reAccountNumber}
                        onChange={handleInputChange}
                        className={errors.reAccountNumber ? 'border-red-500' : ''}
                      />
                      {errors.reAccountNumber && (
                        <p className="text-sm text-red-500">{errors.reAccountNumber}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="ifscCode" className="text-sm font-medium">
                        IFSC Code
                      </label>
                      <Input
                        id="ifscCode"
                        name="ifscCode"
                        type="text"
                        placeholder="Enter IFSC code"
                        value={formData.ifscCode}
                        onChange={handleInputChange}
                        className={errors.ifscCode ? 'border-red-500' : ''}
                      />
                      {errors.ifscCode && (
                        <p className="text-sm text-red-500">{errors.ifscCode}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="accountHolderName" className="text-sm font-medium">
                        Account Holder Name
                      </label>
                      <Input
                        id="accountHolderName"
                        name="accountHolderName"
                        type="text"
                        placeholder="Enter account holder name"
                        value={formData.accountHolderName}
                        onChange={handleInputChange}
                        className={errors.accountHolderName ? 'border-red-500' : ''}
                      />
                      {errors.accountHolderName && (
                        <p className="text-sm text-red-500">{errors.accountHolderName}</p>
                      )}
                    </div>

                    <div className="space-y-2">
                      <label htmlFor="branchName" className="text-sm font-medium">
                        Branch Name
                      </label>
                      <Input
                        id="branchName"
                        name="branchName"
                        type="text"
                        placeholder="Enter branch name"
                        value={formData.branchName}
                        onChange={handleInputChange}
                        className={errors.branchName ? 'border-red-500' : ''}
                      />
                      {errors.branchName && (
                        <p className="text-sm text-red-500">{errors.branchName}</p>
                      )}
                    </div>
                  </div>

                  <div className="mt-6">
                    <Button
                      type="submit"
                      className="w-full bg-customOrange hover:bg-orange-600 text-white"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        'Submit Payment Details'
                      )}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <History className="h-5 w-5 text-orange-600" />
                  Payment History
                </CardTitle>
                <CardDescription>
                  View all your payment transactions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  {loadingHistory ? (
                    <div className="flex items-center justify-center py-8">
                      <Loader2 className="h-6 w-6 animate-spin" />
                      <span className="ml-2">Loading payment history...</span>
                    </div>
                  ) : paymentHistory.length > 0 ? (
                    <table className="w-full border-collapse">
                      <thead>
                        <tr className="border-b">
                          <th className="text-left p-3 font-medium">Date</th>
                          <th className="text-left p-3 font-medium">Bank Name</th>
                          <th className="text-left p-3 font-medium">Account Number</th>
                          <th className="text-left p-3 font-medium">Account Holder</th>
                          <th className="text-left p-3 font-medium">Status</th>
                        </tr>
                      </thead>
                      <tbody>
                        {paymentHistory.map((payment) => (
                          <tr key={payment.id} className="border-b hover:bg-gray-50">
                            <td className="p-3">
                              {new Date(payment.createdAt).toLocaleDateString()}
                            </td>
                            <td className="p-3">{payment.bankName}</td>
                            <td className="p-3">
                              ****{payment.accountNumber.slice(-4)}
                            </td>
                            <td className="p-3">{payment.accountHolderName}</td>
                            <td className="p-3">
                              <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                                Submitted
                              </span>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <p>No payment history found</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
      <Footer />
    </>
  );
};

export default PaymentPage;