import Joi from 'joi';

export const createBankPaymentSchema = Joi.object({
  bankName: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.empty': 'Bank name is required',
      'string.min': 'Bank name must be at least 2 characters long',
      'string.max': 'Bank name cannot exceed 100 characters',
      'any.required': 'Bank name is required'
    }),

  accountNumber: Joi.string()
    .trim()
    .pattern(/^[0-9]{9,18}$/)
    .required()
    .messages({
      'string.empty': 'Account number is required',
      'string.pattern.base': 'Account number must be 9-18 digits',
      'any.required': 'Account number is required'
    }),

  reAccountNumber: Joi.string()
    .trim()
    .valid(Joi.ref('accountNumber'))
    .required()
    .messages({
      'string.empty': 'Please re-enter account number',
      'any.only': 'Account numbers do not match',
      'any.required': 'Please re-enter account number'
    }),

  ifscCode: Joi.string()
    .trim()
    .pattern(/^[A-Z]{4}0[A-Z0-9]{6}$/)
    .required()
    .messages({
      'string.empty': 'IFSC code is required',
      'string.pattern.base': 'Invalid IFSC code format (e.g., SBIN0001234)',
      'any.required': 'IFSC code is required'
    }),

  accountHolderName: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .pattern(/^[A-Za-z\s]+$/)
    .required()
    .messages({
      'string.empty': 'Account holder name is required',
      'string.min': 'Account holder name must be at least 2 characters long',
      'string.max': 'Account holder name cannot exceed 100 characters',
      'string.pattern.base': 'Account holder name can only contain letters and spaces',
      'any.required': 'Account holder name is required'
    }),

  branchName: Joi.string()
    .trim()
    .min(2)
    .max(100)
    .required()
    .messages({
      'string.empty': 'Branch name is required',
      'string.min': 'Branch name must be at least 2 characters long',
      'string.max': 'Branch name cannot exceed 100 characters',
      'any.required': 'Branch name is required'
    })
});

export const updateBankPaymentSchema = createBankPaymentSchema;
