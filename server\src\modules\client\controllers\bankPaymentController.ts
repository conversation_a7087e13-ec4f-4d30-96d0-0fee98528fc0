import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createBankPaymentDetails,
  updateBankPaymentDetails,
  findBankPaymentByClassId,
  getAllBankPaymentDetails,
  findBankPaymentByIdAndClassId,
  deleteBankPaymentById,
  getPaymentHistoryByClassId,
  BankPaymentData
} from '../services/bankPaymentService';

// Create or update bank payment details
export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    } = req.body;

    if (!bankName || !accountNumber || !reAccountNumber || !ifscCode || !accountHolderName || !branchName) {
      return sendError(res, 'All bank details are required', 400);
    }

    // Validate account number confirmation
    if (accountNumber !== reAccountNumber) {
      return sendError(res, 'Account numbers do not match', 400);
    }

    const bankPaymentData: BankPaymentData = {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    };

    // Check if bank payment details already exist for this class
    const existingPayment = await findBankPaymentByClassId(classId);

    let bankPayment;

    if (existingPayment) {
      // Update existing record
      bankPayment = await updateBankPaymentDetails(existingPayment.id, bankPaymentData);
    } else {
      // Create new record
      bankPayment = await createBankPaymentDetails(classId, bankPaymentData);
    }

    return sendSuccess(res, bankPayment, 'Bank payment details saved successfully');
  } catch (error) {
    console.error('Error saving bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get bank payment details for the authenticated class
export const getBankPaymentDetails = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const bankPayment = await findBankPaymentByClassId(classId);

    if (!bankPayment) {
      return sendSuccess(res, null, 'No bank payment details found');
    }

    return sendSuccess(res, bankPayment, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get all bank payment details (Admin only)
export const getAllBankPayments = async (req: Request, res: Response): Promise<any> => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;

    const result = await getAllBankPaymentDetails(
      Number(page),
      Number(limit),
      search as string
    );

    return sendSuccess(res, result, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching all bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Delete bank payment details
export const deleteBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { id } = req.params;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    // Verify the bank payment belongs to the authenticated class
    const bankPayment = await findBankPaymentByIdAndClassId(id, classId);

    if (!bankPayment) {
      return sendError(res, 'Bank payment details not found', 404);
    }

    await deleteBankPaymentById(id);

    return sendSuccess(res, null, 'Bank payment details deleted successfully');
  } catch (error) {
    console.error('Error deleting bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get payment history for the authenticated class
export const getPaymentHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const { page = 1, limit = 10 } = req.query;

    const result = await getPaymentHistoryByClassId(
      classId,
      Number(page),
      Number(limit)
    );

    return sendSuccess(res, result, 'Payment history retrieved successfully');
  } catch (error) {
    console.error('Error fetching payment history:', error);
    return sendError(res, 'Internal server error', 500);
  }
};