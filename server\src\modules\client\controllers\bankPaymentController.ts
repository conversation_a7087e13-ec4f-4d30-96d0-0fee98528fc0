import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createBankPaymentDetails,
  updateBankPaymentDetails,
  findBankPaymentByClassId,
  BankPaymentData
} from '../services/bankPaymentService';
import { createBankPaymentSchema } from '../requests/bankPaymentRequest';

// Create or update bank payment details
export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    // Validate request body using Zod schema
    try {
      const validatedData = createBankPaymentSchema.parse(req.body);

      const {
        bankName,
        accountNumber,
        reAccountNumber,
        ifscCode,
        accountHolderName,
        branchName
      } = validatedData;

      // Check if bank payment details already exist for this class
      const existingPayment = await findBankPaymentByClassId(classId);

      const bankPaymentData: BankPaymentData = {
        bankName,
        accountNumber,
        reAccountNumber,
        ifscCode,
        accountHolderName,
        branchName
      };

      let bankPayment;

      if (existingPayment) {
        // Update existing record
        bankPayment = await updateBankPaymentDetails(existingPayment.id, bankPaymentData);
      } else {
        // Create new record
        bankPayment = await createBankPaymentDetails(classId, bankPaymentData);
      }

      return sendSuccess(res, bankPayment, 'Bank payment details saved successfully');
    } catch (error: any) {
      // Handle Zod validation errors
      if (error.name === 'ZodError') {
        return sendError(res, error.errors[0].message, 400);
      }
      console.error('Error saving bank payment details:', error);
      return sendError(res, 'Internal server error', 500);
    }
  } catch (error) {
    console.error('Error saving bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get bank payment details for the authenticated class
export const getBankPaymentDetails = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const bankPayment = await findBankPaymentByClassId(classId);

    if (!bankPayment) {
      return sendSuccess(res, null, 'No bank payment details found');
    }

    return sendSuccess(res, bankPayment, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};