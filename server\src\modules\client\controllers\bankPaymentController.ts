import { Request, Response } from 'express';
import prisma from '@/config/prismaClient';
import { sendError, sendSuccess } from '@/utils/response';

// Create or update bank payment details
export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    } = req.body;

    // Validate required fields
    if (!bankName || !accountNumber || !reAccountNumber || !ifscCode || !accountHolderName || !branchName) {
      return sendError(res, 'All bank details are required', 400);
    }

    // Validate account number confirmation
    if (accountNumber !== reAccountNumber) {
      return sendError(res, 'Account numbers do not match', 400);
    }

    // Check if bank payment details already exist for this class
    const existingPayment = await prisma.bankPayment.findFirst({
      where: { classId }
    });

    let bankPayment;

    if (existingPayment) {
      // Update existing record
      bankPayment = await prisma.bankPayment.update({
        where: { id: existingPayment.id },
        data: {
          bankName,
          accountNumber,
          reAccountNumber,
          ifscCode,
          accountHolderName,
          branchName,
          updatedAt: new Date()
        }
      });
    } else {
      // Create new record
      bankPayment = await prisma.bankPayment.create({
        data: {
          classId,
          bankName,
          accountNumber,
          reAccountNumber,
          ifscCode,
          accountHolderName,
          branchName
        }
      });
    }

    return sendSuccess(res, 'Bank payment details saved successfully', bankPayment);
  } catch (error) {
    console.error('Error saving bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get bank payment details for the authenticated class
export const getBankPaymentDetails = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const bankPayment = await prisma.bankPayment.findFirst({
      where: { classId },
      include: {
        class: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            className: true,
            email: true
          }
        }
      }
    });

    if (!bankPayment) {
      return sendSuccess(res, 'No bank payment details found', null);
    }

    return sendSuccess(res, 'Bank payment details retrieved successfully', bankPayment);
  } catch (error) {
    console.error('Error fetching bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};