import { Request, Response } from 'express';
import { sendError, sendSuccess } from '@/utils/response';
import {
  createBankPaymentDetails,
  updateBankPaymentDetails,
  findBankPaymentByClassId,
  getAllBankPaymentDetails,
  findBankPaymentByIdAndClassId,
  deleteBankPaymentById,
  getPaymentHistoryByClassId,
  unlockBankPaymentForEdit,
  lockBankPaymentAfterEdit,
  BankPaymentData
} from '../services/bankPaymentService';
import { createBankPaymentSchema } from '../requests/bankPaymentRequest';

// Create or update bank payment details
export const createBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    // Validate request body using Joi schema
    const { error, value } = createBankPaymentSchema.validate(req.body);
    if (error) {
      return sendError(res, error.details[0].message, 400);
    }

    const {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    } = value;

    // Check if bank payment details already exist for this class
    const existingPayment = await findBankPaymentByClassId(classId);

    // If payment exists and is locked, prevent editing
    if (existingPayment && existingPayment.isLocked) {
      return sendError(res, 'Bank payment details are locked. Please unlock to edit.', 403);
    }

    const bankPaymentData: BankPaymentData = {
      bankName,
      accountNumber,
      reAccountNumber,
      ifscCode,
      accountHolderName,
      branchName
    };

    let bankPayment;

    if (existingPayment) {
      // Update existing record
      bankPayment = await updateBankPaymentDetails(existingPayment.id, bankPaymentData);
      // Lock the payment after update
      await lockBankPaymentAfterEdit(classId);
    } else {
      // Create new record (will be locked by default)
      bankPayment = await createBankPaymentDetails(classId, bankPaymentData);
    }

    return sendSuccess(res, bankPayment, 'Bank payment details saved successfully');
  } catch (error) {
    console.error('Error saving bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get bank payment details for the authenticated class
export const getBankPaymentDetails = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const bankPayment = await findBankPaymentByClassId(classId);

    if (!bankPayment) {
      return sendSuccess(res, null, 'No bank payment details found');
    }

    return sendSuccess(res, bankPayment, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get all bank payment details (Admin only)
export const getAllBankPayments = async (req: Request, res: Response): Promise<any> => {
  try {
    const { page = 1, limit = 10, search = '' } = req.query;

    const result = await getAllBankPaymentDetails(
      Number(page),
      Number(limit),
      search as string
    );

    return sendSuccess(res, result, 'Bank payment details retrieved successfully');
  } catch (error) {
    console.error('Error fetching all bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Delete bank payment details
export const deleteBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;
    const { id } = req.params;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    // Verify the bank payment belongs to the authenticated class
    const bankPayment = await findBankPaymentByIdAndClassId(id, classId);

    if (!bankPayment) {
      return sendError(res, 'Bank payment details not found', 404);
    }

    await deleteBankPaymentById(id);

    return sendSuccess(res, null, 'Bank payment details deleted successfully');
  } catch (error) {
    console.error('Error deleting bank payment details:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Get payment history for the authenticated class
export const getPaymentHistory = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    const { page = 1, limit = 10 } = req.query;

    const result = await getPaymentHistoryByClassId(
      classId,
      Number(page),
      Number(limit)
    );

    return sendSuccess(res, result, 'Payment history retrieved successfully');
  } catch (error) {
    console.error('Error fetching payment history:', error);
    return sendError(res, 'Internal server error', 500);
  }
};

// Unlock bank payment for editing
export const unlockBankPayment = async (req: Request, res: Response): Promise<any> => {
  try {
    const classId = req.class?.id;

    if (!classId) {
      return sendError(res, 'Unauthorized: Class ID not found', 401);
    }

    // Check if bank payment exists
    const existingPayment = await findBankPaymentByClassId(classId);
    if (!existingPayment) {
      return sendError(res, 'No bank payment details found', 404);
    }

    // Unlock the payment
    await unlockBankPaymentForEdit(classId);

    return sendSuccess(res, null, 'Bank payment details unlocked for editing');
  } catch (error) {
    console.error('Error unlocking bank payment:', error);
    return sendError(res, 'Internal server error', 500);
  }
};